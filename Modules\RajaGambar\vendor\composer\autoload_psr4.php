<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    '<PERSON><PERSON>\\TemporaryDirectory\\' => array($vendorDir . '/spatie/temporary-directory/src'),
    'Spatie\\Image\\' => array($vendorDir . '/spatie/image/src'),
    'Spatie\\ImageOptimizer\\' => array($vendorDir . '/spatie/image-optimizer/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Mo<PERSON><PERSON>\\RajaGambar\\Tests\\' => array($baseDir . '/tests'),
    'Mo<PERSON>les\\RajaGambar\\Database\\Seeders\\' => array($baseDir . '/database/seeders'),
    'Modules\\RajaGambar\\Database\\Factories\\' => array($baseDir . '/database/factories'),
    'Modu<PERSON>\\RajaGambar\\' => array($baseDir . '/app'),
);
