<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit1e36a05496930f11a90d19850caa00e2
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'Symfony\\Component\\Process\\' => 26,
            '<PERSON><PERSON>\\TemporaryDirectory\\' => 26,
            '<PERSON><PERSON>\\Image\\' => 13,
            '<PERSON><PERSON>\\ImageOptimizer\\' => 22,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
        ),
        'M' => 
        array (
            'Modules\\RajaGambar\\Tests\\' => 25,
            'Modules\\RajaGambar\\Database\\Seeders\\' => 36,
            'Mo<PERSON><PERSON>\\RajaGambar\\Database\\Factories\\' => 38,
            'Mo<PERSON><PERSON>\\RajaGambar\\' => 19,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Symfony\\Component\\Process\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/process',
        ),
        '<PERSON><PERSON>\\TemporaryDirectory\\' => 
        array (
            0 => __DIR__ . '/..' . '/spatie/temporary-directory/src',
        ),
        '<PERSON><PERSON>\\Image\\' => 
        array (
            0 => __DIR__ . '/..' . '/spatie/image/src',
        ),
        'Spatie\\ImageOptimizer\\' => 
        array (
            0 => __DIR__ . '/..' . '/spatie/image-optimizer/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Modules\\RajaGambar\\Tests\\' => 
        array (
            0 => __DIR__ . '/../..' . '/tests',
        ),
        'Modules\\RajaGambar\\Database\\Seeders\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/seeders',
        ),
        'Modules\\RajaGambar\\Database\\Factories\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/factories',
        ),
        'Modules\\RajaGambar\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit1e36a05496930f11a90d19850caa00e2::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit1e36a05496930f11a90d19850caa00e2::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit1e36a05496930f11a90d19850caa00e2::$classMap;

        }, null, ClassLoader::class);
    }
}
