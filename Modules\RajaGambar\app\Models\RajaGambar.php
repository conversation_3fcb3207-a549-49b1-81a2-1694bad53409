<?php 

namespace Modules\RajaGambar\Models;

use App\Traits\PakaiJcol;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\FileAdder;
use Spatie\MediaLibrary\MediaCollections\Models\Media as MediaModel;


 class RajaGambar extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia , PakaiJcol;

    protected $table = 'raja_gambar';

    protected $fillable = [
        'id',
        'file',
        'json',

    ];

    protected $casts = [
        'json' => 'array',
    ];



   }
